<?php

namespace Tqdev\PhpCrudApi\Cache;

use Tqdev\PhpCrudApi\Cache\Base\BaseCache;

class RedisCache extends BaseCache implements Cache
{
    protected $prefix;
    protected $redis;

    public function __construct(string $prefix, string $config)
    {
        $this->prefix = $prefix;
        if ($config == '') {
            $config = '127.0.0.1';
        }
        $params = explode(':', $config, 6);
        if (isset($params[3])) {
            $params[3] = null;
        }
        $this->redis = new \Redis();
        call_user_func_array(array($this->redis, 'pconnect'), $params);
    }

    public function set(string $key, string $value, int $ttl = 0): bool
    {
        return $this->redis->set($this->prefix . $key, $value, $ttl);
    }

    public function get(string $key): string
    {
        return $this->redis->get($this->prefix . $key) ?: '';
    }

    public function clear(): bool
    {
        return $this->redis->flushDb();
    }
}
